package com.facishare.marketing.common.enums;

/**
 * 工作空间同步状态枚举
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public enum WorkspaceSyncStatusEnum {

    /**
     * 初始状态 - 同步任务尚未开始
     */
    INITIAL("INITIAL", "初始状态"),

    /**
     * 同步中 - 正在执行同步任务
     */
    SYNCING("SYNCING", "同步中"),

    /**
     * 同步完成 - 所有资产已成功同步
     */
    COMPLETED("COMPLETED", "同步完成"),

    /**
     * 同步失败 - 同步过程中发生错误
     */
    FAILED("FAILED", "同步失败"),

    /**
     * 未知状态 - Redis中未找到状态信息（可能已过期或从未开始）
     */
    UNKNOWN("UNKNOWN", "未知状态");

    private final String code;
    private final String description;

    WorkspaceSyncStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举，如果未找到则返回UNKNOWN
     */
    public static WorkspaceSyncStatusEnum fromCode(String code) {
        if (code == null) {
            return UNKNOWN;
        }

        for (WorkspaceSyncStatusEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }

        return UNKNOWN;
    }

    /**
     * 判断是否为最终状态（完成或失败）
     *
     * @return true表示是最终状态，false表示还在进行中
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == FAILED;
    }

    /**
     * 判断是否为成功状态
     *
     * @return true表示同步成功
     */
    public boolean isSuccess() {
        return this == COMPLETED;
    }

    /**
     * 判断是否为进行中状态
     *
     * @return true表示正在同步
     */
    public boolean isInProgress() {
        return this == SYNCING;
    }
}
