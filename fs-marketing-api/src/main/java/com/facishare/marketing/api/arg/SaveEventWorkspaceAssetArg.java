package com.facishare.marketing.api.arg;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class SaveEventWorkspaceAssetArg  implements Serializable {
    private String ea;
    private Integer fsUserId;
    private String marketingEventId;
    private String workspaceId;
    private String type;
    private String objectId;

    public boolean isWrongParam() {
        return  StringUtils.isBlank(marketingEventId) || StringUtils.isBlank(type) || StringUtils.isBlank(objectId);
    }

}
