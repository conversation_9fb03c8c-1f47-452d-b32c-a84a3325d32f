/**
 * @IgnoreI18nFile
 */
package com.facishare.marketing.api.data;

import com.facishare.marketing.common.enums.EventActivityStatusEnum;
import com.facishare.marketing.common.model.SmsParamObject;
import com.facishare.marketing.common.util.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 类描述 :
 *
 * <AUTHOR>
 * @Time 2019/7/12.
 * @IgnoreI18nFile
 */
@Data
@ToString
public class MarketingEventData  implements Serializable, SmsParamObject {
    @ApiModelProperty(value = "市场活动ID")
    private String id;
    /**
     * {@link EventActivityStatusEnum}
     */
    @ApiModelProperty(value = "市场活动状态")
    private String bizStatus;
    @ApiModelProperty(value = "市场活动名称")
    private String name;
    @ApiModelProperty(value = "市场活动开始时间")
    private Long beginTime;
    @ApiModelProperty(value = "市场活动结束时间")
    private Long endTime;
    @ApiModelProperty(value = "市场活动类型")
    private String eventType;
    @ApiModelProperty(value = "市场活动位置")
    private String location;
    @ApiModelProperty(value = "实际成本")
    private Double actualCost;
    @ApiModelProperty(value = "实际收入")
    private Double actualIncome;
    @ApiModelProperty(value = "预计成本")
    private Double expectedCost;
    @ApiModelProperty(value = "预计收入")
    private Double expectedIncome;
    @ApiModelProperty(value = "实际投资回报率")
    private Double actualROI;
    @ApiModelProperty(value = "期望投资回报率")
    private Double expectedROI;
    @ApiModelProperty(value = "负责人")
    private Integer ownerId;

    /**
     * 父活动id
     */
    private String parentId;

    private String lifeStatus;

    @ApiModelProperty(value = "创建时间")
    private Long createTime;

    @ApiModelProperty(value = "市场活动封面")
    private String cover;

    private String eventForm;

    @ApiModelProperty(value = "工作空间id")
    private String workspaceObjId;

    public Double getActualROI() {
        if (this.getActualCost() != null && this.getActualIncome() != null && this.getActualCost() != 0.0){
            return this.getActualIncome()/this.getActualCost();
        }
        return this.actualROI;
    }

    public Double getExpectedROI() {
        if (this.getExpectedCost() != null && this.getExpectedIncome() != null && this.getExpectedCost() != 0.0){
            return this.getExpectedIncome()/this.getExpectedCost();
        }
        return this.expectedROI;
    }

    @Override
    public Map<String, String> getParamDescMap() {
        Map<String, String> map = new HashMap<>();
        if (StringUtils.isNotBlank(name)) {
            map.put("marketingEventName", "市场活动名称");
        }
        if (beginTime != null) {
            map.put("startTime", "活动开始时间");
        }
        if (endTime != null) {
            map.put("endTime", "活动结束时间");
        }
        if (StringUtils.isNotBlank(location)) {
            map.put("eventPlace", "活动地点");
        }
        return map;
    }

    @Override
    public Map<String, String> getParamValueMap() {
        Map<String, String> map = new HashMap<>();
        if (StringUtils.isNotBlank(name)) {
            map.put("{市场活动名称}", name);
        }
        if (beginTime != null) {
            map.put("{活动开始时间}", DateUtil.dateMillis2String(beginTime, "yyyy-MM-dd HH:mm"));
        }
        if (endTime != null) {
            map.put("{活动结束时间}", DateUtil.dateMillis2String(endTime, "yyyy-MM-dd HH:mm"));
        }
        if (StringUtils.isNotBlank(location)) {
            map.put("{活动地点}", location);
        }
        return map;
    }
}
