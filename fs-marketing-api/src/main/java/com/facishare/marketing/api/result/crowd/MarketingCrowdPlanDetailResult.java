package com.facishare.marketing.api.result.crowd;

import com.facishare.marketing.api.data.MarketingUserGroupData;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("目标人群运营计划详情")
public class MarketingCrowdPlanDetailResult implements Serializable {

    @ApiModelProperty("市场活动")
    private ObjectData marketingEvent;
    @ApiModelProperty("目标人群")
    private MarketingUserGroupData marketingUserGroup;
    @ApiModelProperty("定时任务设置")
    private TimedTaskSetting timedTaskSetting;
    @ApiModelProperty("定时任务状态")
    private String timedTaskStatus;//todo:进行中； executing：执行中； executed：已结束； canceled：已取消； error：数据异常
    @ApiModelProperty(value = "工作空间id")
    private String workspaceObjId;

    @Data
    @ApiModel("定时任务设置")
    public static class TimedTaskSetting implements Serializable {
        @ApiModelProperty("开始时间")
        private Long repeatRangeStart;
        @ApiModelProperty("结束时间")
        private Long repeatRangeEnd;
        @ApiModelProperty("类型")
        private Integer repeatType;
        @ApiModelProperty("重复日期(包含)")
        List<Integer> includeRepeatValue;
        @ApiModelProperty("重复日期(具体)")
        private List<Integer> repeatValue;
        @ApiModelProperty("重复时间:分钟")
        private Integer triggerAtMinutes;
        @ApiModelProperty("单次运营周期")
        private Integer singlePeriod;
        @ApiModelProperty("参与限制:1-仅参与1次;2-参与多次")
        private Integer joinLimit;
        @ApiModelProperty("限制天数")
        private Integer limitDay;
        @ApiModelProperty("次数")
        private Integer limit;
    }

}
