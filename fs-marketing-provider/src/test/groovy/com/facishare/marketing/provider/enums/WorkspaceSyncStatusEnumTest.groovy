package com.facishare.marketing.provider.enums

import com.facishare.marketing.common.enums.WorkspaceSyncStatusEnum
import spock.lang.Specification

/**
 * 工作空间同步状态枚举测试
 */
class WorkspaceSyncStatusEnumTest extends Specification {

    def "测试枚举基本属性"() {
        expect:
        WorkspaceSyncStatusEnum.INITIAL.getCode() == "INITIAL"
        WorkspaceSyncStatusEnum.INITIAL.getDescription() == "初始状态"
        
        WorkspaceSyncStatusEnum.SYNCING.getCode() == "SYNCING"
        WorkspaceSyncStatusEnum.SYNCING.getDescription() == "同步中"
        
        WorkspaceSyncStatusEnum.COMPLETED.getCode() == "COMPLETED"
        WorkspaceSyncStatusEnum.COMPLETED.getDescription() == "同步完成"
        
        WorkspaceSyncStatusEnum.FAILED.getCode() == "FAILED"
        WorkspaceSyncStatusEnum.FAILED.getDescription() == "同步失败"
        
        WorkspaceSyncStatusEnum.UNKNOWN.getCode() == "UNKNOWN"
        WorkspaceSyncStatusEnum.UNKNOWN.getDescription() == "未知状态"
    }

    def "测试fromCode方法"() {
        expect:
        WorkspaceSyncStatusEnum.fromCode(code) == expectedEnum

        where:
        code        | expectedEnum
        "INITIAL"   | WorkspaceSyncStatusEnum.INITIAL
        "SYNCING"   | WorkspaceSyncStatusEnum.SYNCING
        "COMPLETED" | WorkspaceSyncStatusEnum.COMPLETED
        "FAILED"    | WorkspaceSyncStatusEnum.FAILED
        "UNKNOWN"   | WorkspaceSyncStatusEnum.UNKNOWN
        null        | WorkspaceSyncStatusEnum.UNKNOWN
        ""          | WorkspaceSyncStatusEnum.UNKNOWN
        "INVALID"   | WorkspaceSyncStatusEnum.UNKNOWN
    }

    def "测试状态判断方法"() {
        expect:
        status.isFinalStatus() == expectedFinal
        status.isSuccess() == expectedSuccess
        status.isInProgress() == expectedInProgress

        where:
        status                              | expectedFinal | expectedSuccess | expectedInProgress
        WorkspaceSyncStatusEnum.INITIAL     | false         | false           | false
        WorkspaceSyncStatusEnum.SYNCING     | false         | false           | true
        WorkspaceSyncStatusEnum.COMPLETED   | true          | true            | false
        WorkspaceSyncStatusEnum.FAILED      | true          | false           | false
        WorkspaceSyncStatusEnum.UNKNOWN     | false         | false           | false
    }

    def "测试所有枚举值"() {
        when:
        def allValues = WorkspaceSyncStatusEnum.values()

        then:
        allValues.length == 5
        allValues.contains(WorkspaceSyncStatusEnum.INITIAL)
        allValues.contains(WorkspaceSyncStatusEnum.SYNCING)
        allValues.contains(WorkspaceSyncStatusEnum.COMPLETED)
        allValues.contains(WorkspaceSyncStatusEnum.FAILED)
        allValues.contains(WorkspaceSyncStatusEnum.UNKNOWN)
    }
}
