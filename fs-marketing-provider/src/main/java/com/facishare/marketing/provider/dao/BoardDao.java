package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.BoardEntity;
import com.facishare.marketing.provider.entity.BoardMenuEntity;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/*
 * <AUTHOR>
 * @Date 2020/8/5 13:36
 * @Version 1.0
 */
public interface BoardDao {
    @Insert("<script> " +
            "insert into board(id, ea, name, description, visible_range, type, object_api_name, object_id, creator, cover, create_time, update_time, template_type, default_template_id, scene_type" +
            "<if test = 'associatedObjectType != null'>, associated_object_type</if>" +
            "<if test = 'marketingActivityType != null'>, marketing_activity_type</if>" +
            "<if test = 'marketingActivityId != null'>, marketing_activity_id</if>" +
            "<if test = 'marketingEventType != null'>, marketing_event_type</if>" +
            "<if test = 'marketingEventId != null'>, marketing_event_id</if>" +
            "<if test = 'goalType != null'>, goal_type</if>" +
            ") " +
            "values(#{id}, #{ea}, #{name}, #{description}, #{visibleRange}, #{type}, #{objectApiName}, #{objectId}, #{creator}, #{cover}, now(), now(), #{templateType}, #{defaultTemplateId}, #{sceneType}"  +
            "<if test = 'associatedObjectType != null'>, #{associatedObjectType} </if>" +
            "<if test = 'marketingActivityType != null'>, #{marketingActivityType} </if>" +
            "<if test = 'marketingActivityId != null'>, #{marketingActivityId} </if>" +
            "<if test = 'marketingEventType != null'>, #{marketingEventType} </if>" +
            "<if test = 'marketingEventId != null'>, #{marketingEventId} </if>" +
            "<if test = 'goalType != null'>, #{goalType} </if>" +
            ")" +
        "</script>")
    int insertBoard(BoardEntity boardEntity);

    @Select("select id from board where ea = #{ea} and visible_range = 'public' and life_status = 0 AND type != 'sop'")
    List<String> listBoardIdsWithPublicVisibleRange(@Param("ea") String ea);

    @Select("select * from board where id = #{boardId} AND ea = #{ea} AND life_status = 0")
    BoardEntity getBoardByBoardId(@Param("boardId") String boardId, @Param("ea") String ea);

    @Select("select * from board where id = #{boardId} AND life_status = 0")
    BoardEntity getBoardByOnlyBoardId(@Param("boardId") String boardId);

    @Select("select name from board where id = #{boardId} AND ea = #{ea} AND life_status = 0")
    String getBoardNameByBoardId(@Param("boardId") String boardId, @Param("ea") String ea);

    @Select("<script> " +
            "  select * from board where id in " +
            "  <foreach collection = 'ids' item = 'id' open = '(' separator = ',' close = ')'> #{id} </foreach> " +
            "  and life_status = 0 AND type != 'sop' ORDER BY create_time DESC " +
            "</script>")
    List<BoardEntity> listBoardByIds(@Param("ids") List<String> ids);

    @Update("update board set name = #{name}, update_time = now() where id = #{id} and life_status = 0")
    int updateBoardNameByBoardId(@Param("id") String id, @Param("name") String name);

    @Update("update board set life_status = 1, update_time = now() where id = #{id}")
    boolean deleteBoardById(@Param("id") String id);

    @Update("update board set visible_range = #{visibleRange}, update_time = now() where id = #{id} and life_status = 0")
    int updateBoardVisibleRangeById(@Param("id") String id, @Param("visibleRange") String visibleRange);


    @Select("<script>"
        + " SELECT id,name FROM board WHERE life_status = 0 AND id IN "
        +     "<foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>"
        +     "#{item}"
        +     "</foreach>"
        + "</script>")
    List<BoardEntity> getBoardNameByIds(@Param("ids") List<String> ids);

    @Select("<script> SELECT * FROM board WHERE ea = #{ea} <if test = 'type != null'> AND type = #{type} </if> AND life_status = 0 </script>")
    List<BoardEntity> listBoardByEaAndType(@Param("ea") String ea, @Param("type") String type);

    @Select("SELECT * FROM board WHERE ea = #{ea} AND template_type = 1 AND life_status = 0 ORDER BY create_time DESC")
    List<BoardEntity> listEnterpriseTemplateBoard(@Param("ea") String ea);

    @Update("UPDATE board SET associated_object_type=#{associatedObjectType}, distribute_plan_id=#{distributePlanId}, marketing_activity_type=#{marketingActivityType}, marketing_activity_id=#{marketingActivityId},"+
            "marketing_event_id=#{marketingEventId}, marketing_event_type=#{marketingEventType}, goal_user_group_id=#{goalUserGroupId}, goal_type=#{goalType}, goal_value=#{goalValue}, " +
            "update_time = now() WHERE id = #{id} AND life_status = 0")
    int updateBoardMenu(BoardEntity boardEntity);

    @Select("SELECT * FROM board WHERE id=#{boardId} AND ea=#{ea} AND associated_object_type!='common' AND goal_reached_notify_sent=true AND goal_value is not null AND life_status = 0 AND " +
        "((associated_object_type = 'marketing_activity_data' AND marketing_activity_id is not null) " +
        " OR (associated_object_type = 'marketing_event_data'  AND marketing_event_id is not null)" +
        " OR (associated_object_type = 'distribute_plan_data' AND distribute_plan_id is not null))")
    BoardEntity getNeedResetCheckGoalReachedTaskById(@Param("ea") String ea, @Param("boardId") String boardCardId);

    @Update("UPDATE board SET goal_reached_notify_sent=#{toStatus} WHERE id=#{boardId} AND ea=#{ea} AND goal_reached_notify_sent=#{fromStatus} AND life_status = 0")
    int changeBoardGoalReachedNotifySentStatus(@Param("ea") String ea, @Param("boardId")String boardId, @Param("fromStatus") boolean fromStatus, @Param("toStatus") boolean toStatus);

    @Select("SELECT id, ea, goal_type, goal_value, goal_reached_notify_sent, default_template_id, marketing_activity_type, marketing_activity_id,marketing_event_type,marketing_event_id,"+
            "distribute_plan_id, associated_object_type, goal_user_group_id, template_type FROM board WHERE id = #{boardId} AND ea = #{ea} AND life_status = 0")
    BoardMenuEntity queryMenuById(@Param("ea") String ea, @Param("boardId") String boardId);

    @Select("SELECT cover FROM board WHERE id = #{id} AND ea = #{ea} AND template_type = 1 AND life_status = 0")
    String queryCoverFromEnterpriseTemplate(@Param("ea") String ea, @Param("id") String templateId);

    @Select("SELECT * FROM board WHERE id = #{id} AND ea = #{ea} AND type != 'sop'")
    BoardEntity getByIdWhateverLiveStatus(@Param("id") String id, @Param("ea") String ea);

    @Select("SELECT scene_type FROM board WHERE id = #{id} AND ea = #{ea}")
    String getSceneTypeById(@Param("id") String boardId, @Param("ea") String ea);

    @Select("SELECT * FROM board WHERE ea = #{ea} AND template_type = 1 AND scene_type = #{sceneType} AND life_status = 0 ORDER BY create_time DESC")
    List<BoardEntity> listEnterpriseTemplateBoardBySceneType(@Param("ea") String ea, @Param("sceneType") String sceneType);

    @Update("update board set name = #{name}, scene_type = #{sceneType}, cover = #{cover}, update_time = now() where id = #{id} and life_status = 0")
    int updateTemplateInfo(@Param("id") String id, @Param("name") String name, @Param("sceneType") String sceneType, @Param("cover") String cover);

    @Select("SELECT id FROM board WHERE ea = #{ea} AND type = 'sop' AND object_id = #{objectId} AND template_type = 0 AND life_status = 0 ORDER BY create_time DESC LIMIT 1")
    String getSopBoardIdByObjectId(@Param("ea") String ea, @Param("objectId") String objectId);


    @Update("update board set  object_id = #{objectId}, type = #{type}, update_time = now() where id = #{id} and ea = #{ea}")
    boolean updateObjectEventId(@Param("id") String id,@Param("ea") String ea, @Param("objectId") String objectId, @Param("type") String type);
}
