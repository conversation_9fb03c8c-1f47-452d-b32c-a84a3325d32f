package com.facishare.marketing.provider.dao;

import com.facishare.marketing.provider.entity.MarketingSceneEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * Created on 2021-02-26.
 */

public interface MarketingSceneDao {
	@Insert("INSERT INTO marketing_scene(id, ea, scene_type, target_id, create_time, update_time) " +
		"VALUES(#{e.id}, #{e.ea}, #{e.sceneType}, #{e.targetId}, NOW(), NOW()) ON CONFLICT DO NOTHING")
	int insertScene(@Param("e") MarketingSceneEntity marketingScene);
	
	@Select("<script>" +
		"SELECT * FROM marketing_scene WHERE ea=#{ea} AND scene_type=#{sceneType}" +
		"<if test='targetId != null'> AND target_id=#{targetId}</if>" +
		"</script>")
	MarketingSceneEntity getMarketingSceneByTargetId(@Param("ea") String ea, @Param("sceneType") String sceneType, @Param("targetId") String targetId);
	
	@Select("SELECT * FROM marketing_scene WHERE ea=#{ea} AND id=#{id}")
	MarketingSceneEntity getMarketingSceneById(@Param("ea") String ea, @Param("id") String id);

	@Select("<script>" +
			"SELECT * FROM marketing_scene WHERE ea=#{ea} AND target_id=#{targetId} limit 1" +
			"</script>")
	MarketingSceneEntity getSceneByTargetId(@Param("ea") String ea,  @Param("targetId") String targetId);

}
